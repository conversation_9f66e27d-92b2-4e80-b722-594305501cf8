"use client"

import { useState } from "react"
import {
  CalendarDays,
  DollarSign,
  Eye,
  Heart,
  Play,
  TrendingUp,
  Upload,
  Users,
  Sparkles,
  PlusCircle,
  Clock,
  CheckCircle,
  XCircle,
} from "lucide-react"
import Image from "next/image"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { CreateModelModal } from "@/components/create-model-modal"
import { UseModelModal } from "@/components/use-model-modal" // Import the new modal

export default function SpireClubDashboard() {
  const [selectedPeriod, setSelectedPeriod] = useState("7d")
  const [showCreateModelModal, setShowCreateModelModal] = useState(false)
  const [showUseModelModal, setShowUseModelModal] = useState(false) // State for the new modal
  const [selectedModel, setSelectedModel] = useState<{ name: string; prompt: string } | null>(null) // State to hold selected model info

  const performanceMetrics = {
    subscribers: { current: 2847, change: 12.5, trend: "up" },
    revenue: { current: 8420, change: 8.2, trend: "up" },
    sessionDuration: { current: 14.2, change: -2.1, trend: "down" },
    engagement: { current: 87.3, change: 5.4, trend: "up" },
  }

  const recentMedia = [
    {
      id: 1,
      type: "image",
      title: "Beach Photoshoot",
      views: 1240,
      likes: 89,
      revenue: 245,
      thumbnail: "/placeholder.svg?height=120&width=120",
    },
    {
      id: 2,
      type: "video",
      title: "Morning Routine",
      views: 2100,
      likes: 156,
      revenue: 420,
      thumbnail: "/placeholder.svg?height=120&width=120",
    },
    {
      id: 3,
      type: "image",
      title: "Studio Session",
      views: 890,
      likes: 67,
      revenue: 180,
      thumbnail: "/placeholder.svg?height=120&width=120",
    },
    {
      id: 4,
      type: "video",
      title: "Q&A Session",
      views: 1560,
      likes: 134,
      revenue: 310,
      thumbnail: "/placeholder.svg?height=120&width=120",
    },
  ]

  const fanAnalytics = [
    { metric: "New Subscribers", value: 156, change: 23 },
    { metric: "Active Fans", value: 1240, change: 8 },
    { metric: "Messages Sent", value: 89, change: -5 },
    { metric: "Tips Received", value: 34, change: 45 },
  ]

  const aiModels = [
    {
      id: "model_1",
      name: "Beach Vibes Generator",
      prompt: "Generates images of models on a sunny beach with clear blue water and palm trees.",
      status: "online", // Changed from "active"
      createdAt: "2024-07-20T10:00:00Z",
    },
    {
      id: "model_2",
      name: "Urban Street Style",
      prompt: "Creates fashion photography in a gritty urban environment with neon lights and graffiti.",
      status: "busy", // Changed from "training"
      createdAt: "2024-07-22T14:30:00Z",
    },
    {
      id: "model_3",
      name: "Cozy Indoor Portraits",
      prompt: "Produces warm, inviting portraits in a home setting with soft natural light.",
      status: "offline", // Changed from "failed"
      createdAt: "2024-07-18T08:15:00Z",
    },
    {
      id: "model_4",
      name: "Abstract Art Creator",
      prompt: "Generates abstract art pieces with vibrant colors and dynamic forms.",
      status: "error", // New status
      createdAt: "2024-07-25T11:00:00Z",
    },
    {
      id: "model_5",
      name: "Vintage Fashion Stylist",
      prompt: "Suggests vintage fashion outfits based on a given era and occasion.",
      status: "paused", // New status
      createdAt: "2024-07-28T09:45:00Z",
    },
  ]

  const handleUseModelClick = (model: { name: string; prompt: string }) => {
    setSelectedModel(model)
    setShowUseModelModal(true)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 dark">
      <div className="container mx-auto p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent">
              SpireClub Dashboard
            </h1>
            <p className="text-muted-foreground mt-1">{"Welcome to your SpireClub performance overview."}</p>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              className="border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white bg-transparent"
            >
              <Upload className="w-4 h-4 mr-2" />
              Upload Content
            </Button>
          </div>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5 lg:w-[500px]">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="media">Media</TabsTrigger>
            <TabsTrigger value="fans">Fans</TabsTrigger>
            <TabsTrigger value="models">Models</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Performance Metrics */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card className="border-purple-200 dark:border-purple-800">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Subscribers</CardTitle>
                  <Users className="h-4 w-4 text-purple-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{performanceMetrics.subscribers.current.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    <span className="text-green-600">+{performanceMetrics.subscribers.change}%</span> from last week
                  </p>
                </CardContent>
              </Card>

              <Card className="border-green-200 dark:border-green-800">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Revenue</CardTitle>
                  <DollarSign className="h-4 w-4 text-green-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">${performanceMetrics.revenue.current.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    <span className="text-green-600">+{performanceMetrics.revenue.change}%</span> from last week
                  </p>
                </CardContent>
              </Card>

              <Card className="border-blue-200 dark:border-blue-800">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg. Session</CardTitle>
                  <CalendarDays className="h-4 w-4 text-blue-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{performanceMetrics.sessionDuration.current}m</div>
                  <p className="text-xs text-muted-foreground">
                    <span className="text-red-600">{performanceMetrics.sessionDuration.change}%</span> from last week
                  </p>
                </CardContent>
              </Card>

              <Card className="border-pink-200 dark:border-pink-800">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Engagement Rate</CardTitle>
                  <Heart className="h-4 w-4 text-pink-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{performanceMetrics.engagement.current}%</div>
                  <p className="text-xs text-muted-foreground">
                    <span className="text-green-600">+{performanceMetrics.engagement.change}%</span> from last week
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Recent Performance */}
            <div className="grid gap-6 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Revenue Breakdown</CardTitle>
                  <CardDescription>Your earnings by content type this week</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Subscriptions</span>
                      <span className="font-medium">$4,200 (50%)</span>
                    </div>
                    <Progress value={50} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Tips</span>
                      <span className="font-medium">$2,520 (30%)</span>
                    </div>
                    <Progress value={30} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Pay-per-view</span>
                      <span className="font-medium">$1,700 (20%)</span>
                    </div>
                    <Progress value={20} className="h-2" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Top Performing Content</CardTitle>
                  <CardDescription>Your most successful uploads this week</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentMedia.slice(0, 3).map((item) => (
                      <div key={item.id} className="flex items-center space-x-4">
                        <div className="relative">
                          <Image
                            src={item.thumbnail || "/placeholder.svg"}
                            alt={item.title}
                            width={48}
                            height={48}
                            className="rounded-lg object-cover"
                          />
                          {item.type === "video" && <Play className="absolute inset-0 m-auto w-4 h-4 text-white" />}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">{item.title}</p>
                          <p className="text-xs text-muted-foreground">
                            {item.views} views • ${item.revenue} earned
                          </p>
                        </div>
                        <TrendingUp className="w-4 h-4 text-green-600" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Fan Analytics</CardTitle>
                  <CardDescription>Engagement trends and behavior insights</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {fanAnalytics.map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm font-medium">{item.metric}</span>
                        <div className="flex items-center gap-2">
                          <span className="text-lg font-bold">{item.value}</span>
                          <Badge variant={item.change > 0 ? "default" : "secondary"} className="text-xs">
                            {item.change > 0 ? "+" : ""}
                            {item.change}%
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Peak Activity Hours</CardTitle>
                  <CardDescription>When your fans are most active</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[
                      { time: "8:00 PM - 10:00 PM", activity: 95 },
                      { time: "6:00 PM - 8:00 PM", activity: 78 },
                      { time: "10:00 PM - 12:00 AM", activity: 65 },
                      { time: "12:00 PM - 2:00 PM", activity: 45 },
                      { time: "2:00 PM - 4:00 PM", activity: 32 },
                    ].map((slot, index) => (
                      <div key={index} className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span>{slot.time}</span>
                          <span className="font-medium">{slot.activity}%</span>
                        </div>
                        <Progress value={slot.activity} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="media" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Media Library</CardTitle>
                <CardDescription>Manage your content and track performance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {recentMedia.map((item) => (
                    <Card key={item.id} className="overflow-hidden">
                      <div className="relative aspect-video">
                        <Image
                          src={item.thumbnail || "/placeholder.svg"}
                          alt={item.title}
                          fill
                          className="object-cover"
                        />
                        {item.type === "video" && (
                          <div className="absolute inset-0 flex items-center justify-center">
                            <Play className="w-8 h-8 text-white bg-black/50 rounded-full p-2" />
                          </div>
                        )}
                        <Badge className="absolute top-2 right-2" variant="secondary">
                          {item.type}
                        </Badge>
                      </div>
                      <CardContent className="p-4">
                        <h3 className="font-medium mb-2">{item.title}</h3>
                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                          <div className="flex items-center gap-4">
                            <span className="flex items-center gap-1">
                              <Eye className="w-3 h-3" />
                              {item.views}
                            </span>
                            <span className="flex items-center gap-1">
                              <Heart className="w-3 h-3" />
                              {item.likes}
                            </span>
                          </div>
                          <span className="font-medium text-green-600">${item.revenue}</span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="fans" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Subscriber Growth</CardTitle>
                  <CardDescription>Track your audience expansion</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-purple-600">2,847</div>
                      <p className="text-sm text-muted-foreground">Total Subscribers</p>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-center">
                      <div>
                        <div className="text-xl font-semibold text-green-600">+156</div>
                        <p className="text-xs text-muted-foreground">This Week</p>
                      </div>
                      <div>
                        <div className="text-xl font-semibold text-blue-600">+423</div>
                        <p className="text-xs text-muted-foreground">This Month</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Fan Engagement</CardTitle>
                  <CardDescription>How your audience interacts with your content</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Average Likes per Post</span>
                      <span className="font-medium">127</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Comments per Post</span>
                      <span className="font-medium">34</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Share Rate</span>
                      <span className="font-medium">12%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Return Visitor Rate</span>
                      <span className="font-medium">78%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="models" className="space-y-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Sparkles className="w-5 h-5 text-purple-400" />
                    Your AI Models
                  </CardTitle>
                  <CardDescription className="text-xs text-gray-400 line-clamp-2">
                    Manage your custom AI content generation models
                  </CardDescription>
                </div>
                <Button
                  size="sm"
                  onClick={() => setShowCreateModelModal(true)}
                  className="bg-gradient-to-r from-yellow-400 to-yellow-600 hover:from-yellow-500 hover:to-yellow-700 text-black font-semibold"
                >
                  <PlusCircle className="w-4 h-4 mr-2" />
                  Create New Model
                </Button>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {aiModels.map((model) => (
                    <Card key={model.id} className="border-gray-700 bg-gray-800">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center gap-2">
                          <Sparkles className="w-5 h-5 text-purple-400" />
                          {model.name}
                        </CardTitle>
                        <CardDescription className="text-xs text-gray-400 line-clamp-2">{model.prompt}</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-400">Status:</span>
                          {model.status === "online" && (
                            <Badge className="bg-green-600 text-white">
                              <CheckCircle className="w-3 h-3 mr-1" /> Online
                            </Badge>
                          )}
                          {model.status === "busy" && (
                            <Badge className="bg-yellow-600 text-white">
                              <Clock className="w-3 h-3 mr-1 animate-spin" /> Busy
                            </Badge>
                          )}
                          {model.status === "offline" && (
                            <Badge className="bg-gray-600 text-white">
                              <XCircle className="w-3 h-3 mr-1" /> Offline
                            </Badge>
                          )}
                          {model.status === "error" && (
                            <Badge className="bg-red-600 text-white">
                              <XCircle className="w-3 h-3 mr-1" /> Error
                            </Badge>
                          )}
                          {model.status === "paused" && (
                            <Badge className="bg-blue-600 text-white">
                              <Clock className="w-3 h-3 mr-1" /> Paused
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>Created:</span>
                          <span>{new Date(model.createdAt).toLocaleDateString()}</span>
                        </div>
                        <div className="flex gap-2 pt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white bg-transparent"
                            onClick={() => handleUseModelClick(model)} // Open the new modal
                          >
                            Connect
                          </Button>
                          <Button variant="ghost" size="sm" className="text-red-400 hover:bg-red-900/20">
                            Delete
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Create Model Modal */}
        <CreateModelModal isOpen={showCreateModelModal} onClose={() => setShowCreateModelModal(false)} />

        {/* Use Model Modal */}
        {selectedModel && (
          <UseModelModal
            isOpen={showUseModelModal}
            onClose={() => setShowUseModelModal(false)}
            modelName={selectedModel.name}
            modelPrompt={selectedModel.prompt}
          />
        )}
      </div>
    </div>
  )
}
