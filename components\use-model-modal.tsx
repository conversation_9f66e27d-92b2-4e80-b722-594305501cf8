"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>, User } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"

interface UseModelModalProps {
  isOpen: boolean
  onClose: () => void
  modelName: string
  modelPrompt: string
}

interface ChatMessage {
  id: number
  sender: "user" | "ai"
  text: string
}

export function UseModelModal({ isOpen, onClose, modelName, modelPrompt }: UseModelModalProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [userInput, setUserInput] = useState("")
  const [isSending, setIsSending] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Add initial AI message when modal opens
    if (isOpen && messages.length === 0) {
      setMessages([
        {
          id: 1,
          sender: "ai",
          text: `Hello! I am the "${modelName}" AI model. My purpose is to "${modelPrompt}". How can I assist you today?`,
        },
      ])
    }
  }, [isOpen, messages.length, modelName, modelPrompt])

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault()
    if (userInput.trim() === "") return

    const newUserMessage: ChatMessage = {
      id: messages.length + 1,
      sender: "user",
      text: userInput.trim(),
    }
    setMessages((prev) => [...prev, newUserMessage])
    setUserInput("")
    setIsSending(true)

    // Simulate AI response
    await new Promise((resolve) => setTimeout(resolve, 1500))

    const aiResponse: ChatMessage = {
      id: messages.length + 2,
      sender: "ai",
      text: `Based on your input "${newUserMessage.text}", I can generate content related to ${modelPrompt.toLowerCase()}. What specific details would you like to include?`,
    }
    setMessages((prev) => [...prev, aiResponse])
    setIsSending(false)
  }

  const handleClose = () => {
    setMessages([]) // Clear messages on close
    setUserInput("")
    setIsSending(false)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-3xl h-[80vh] flex flex-col bg-gray-900 border-gray-700 text-white dark">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-xl font-bold bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-purple-400" />
            Use Model: {modelName}
          </DialogTitle>
          <DialogDescription className="text-gray-400 line-clamp-1">Prompt: {modelPrompt}</DialogDescription>
        </DialogHeader>

        <Card className="flex-1 flex flex-col border-gray-700 bg-gray-800 overflow-hidden">
          <CardContent className="flex-1 p-4 overflow-hidden">
            <ScrollArea className="h-full pr-4">
              <div className="space-y-4">
                {messages.map((msg) => (
                  <div
                    key={msg.id}
                    className={`flex items-start gap-3 ${msg.sender === "user" ? "justify-end" : "justify-start"}`}
                  >
                    {msg.sender === "ai" && (
                      <div className="w-8 h-8 flex-shrink-0 rounded-full bg-purple-600 flex items-center justify-center text-white">
                        <Sparkles className="w-4 h-4" />
                      </div>
                    )}
                    <div
                      className={`max-w-[70%] p-3 rounded-lg ${
                        msg.sender === "user"
                          ? "bg-yellow-600 text-black rounded-br-none"
                          : "bg-gray-700 text-white rounded-bl-none"
                      }`}
                    >
                      <p className="text-sm">{msg.text}</p>
                    </div>
                    {msg.sender === "user" && (
                      <div className="w-8 h-8 flex-shrink-0 rounded-full bg-gray-600 flex items-center justify-center text-white">
                        <User className="w-4 h-4" />
                      </div>
                    )}
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>
          </CardContent>
          <CardFooter className="p-4 border-t border-gray-700">
            <form onSubmit={handleSendMessage} className="flex w-full gap-2">
              <Input
                placeholder="Type your message..."
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                className="flex-1 bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500"
                disabled={isSending}
              />
              <Button
                type="submit"
                disabled={isSending || userInput.trim() === ""}
                className="bg-gradient-to-r from-purple-500 to-purple-700 hover:from-purple-600 hover:to-purple-800 text-white font-semibold"
              >
                {isSending ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Send className="w-4 h-4" />
                )}
              </Button>
            </form>
          </CardFooter>
        </Card>
      </DialogContent>
    </Dialog>
  )
}
