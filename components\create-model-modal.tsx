"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON><PERSON>, Wand2, Save, X } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Card, CardContent } from "@/components/ui/card"

interface CreateModelModalProps {
  isOpen: boolean
  onClose: () => void
}

export function CreateModelModal({ isOpen, onClose }: CreateModelModalProps) {
  const [formData, setFormData] = useState({
    name: "",
    prompt: "",
  })
  const [isCreating, setIsCreating] = useState(false)

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsCreating(true)

    // Simulate model creation process
    await new Promise((resolve) => setTimeout(resolve, 2000))

    console.log("Creating model with:", formData)

    // Reset form and close modal
    setFormData({ name: "", prompt: "" })
    setIsCreating(false)
    onClose()
  }

  const handleClose = () => {
    if (!isCreating) {
      setFormData({ name: "", prompt: "" })
      onClose()
    }
  }

  const isFormValid = formData.name.trim() !== "" && formData.prompt.trim() !== ""

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl bg-gray-900 border-gray-700 text-white dark">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-purple-400" />
            Create AI Model
          </DialogTitle>
          <DialogDescription className="text-gray-400">
            Create a custom AI model for generating personalized content
          </DialogDescription>
        </DialogHeader>

        <Card className="border-gray-700 bg-gray-800">
          <CardContent className="p-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Model Name Field */}
              <div className="space-y-2">
                <Label htmlFor="modelName" className="text-sm font-medium text-gray-200">
                  Model Name *
                </Label>
                <Input
                  id="modelName"
                  type="text"
                  placeholder="Enter a name for your AI model (e.g., 'Beach Vibes Generator')"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  className="bg-gray-800 border-gray-600 text-white placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500"
                  disabled={isCreating}
                  maxLength={50}
                />
                <p className="text-xs text-gray-400">{formData.name.length}/50 characters</p>
              </div>

              {/* Prompt Field */}
              <div className="space-y-2">
                <Label htmlFor="prompt" className="text-sm font-medium text-gray-200">
                  AI Prompt *
                </Label>
                <Textarea
                  id="prompt"
                  placeholder="Describe what you want your AI model to generate. Be specific about style, mood, setting, and any other details..."
                  value={formData.prompt}
                  onChange={(e) => handleInputChange("prompt", e.target.value)}
                  className="bg-gray-800 border-gray-600 text-white placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500 min-h-[120px] resize-none"
                  disabled={isCreating}
                  maxLength={500}
                />
                <p className="text-xs text-gray-400">{formData.prompt.length}/500 characters</p>
              </div>

              {/* Example Prompts */}
              <div className="bg-gray-700/50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-purple-400 mb-2 flex items-center gap-1">
                  <Wand2 className="w-4 h-4" />
                  Example Prompts
                </h4>
                <div className="space-y-2 text-xs text-gray-300">
                  <p>• "Professional portrait photography with soft lighting and elegant poses"</p>
                  <p>• "Casual lifestyle content with warm, natural lighting and cozy settings"</p>
                  <p>• "Fashion photography with bold colors and dynamic poses"</p>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-end gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  disabled={isCreating}
                  className="border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white bg-transparent"
                >
                  <X className="w-4 h-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={!isFormValid || isCreating}
                  className="bg-gradient-to-r from-purple-500 to-purple-700 hover:from-purple-600 hover:to-purple-800 text-white font-semibold"
                >
                  {isCreating ? (
                    <>
                      <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Creating Model...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Create Model
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Additional Info */}
        <div className="bg-purple-900/20 border border-purple-800/30 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <Sparkles className="w-5 h-5 text-purple-400 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="text-purple-300 font-medium mb-1">AI Model Creation</p>
              <p className="text-gray-400 text-xs leading-relaxed">
                Your AI model will be trained based on your prompt and can be used to generate consistent, personalized
                content. The creation process may take a few minutes to complete.
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
